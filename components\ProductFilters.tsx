'use client'

import { useState } from 'react'
import { XMarkIcon, ChevronRightIcon } from '@heroicons/react/24/outline'

interface ProductFiltersProps {
  isOpen: boolean
  onClose: () => void
}

export default function ProductFilters({ isOpen, onClose }: ProductFiltersProps) {
  const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>({})

  const filterCategories = [
    {
      name: 'Categories',
      options: ['Clothing', 'Shoes', 'Bags', 'Accessories']
    },
    {
      name: 'Designer',
      options: ['Bottega Veneta', 'Gucci', 'Prada', 'Saint Laurent', 'The Row']
    },
    {
      name: 'Colour',
      options: ['Black', 'White', 'Brown', 'Blue', 'Green', 'Red']
    },
    {
      name: 'Clothes Size',
      options: ['XS', 'S', 'M', 'L', 'XL', 'XXL']
    },
    {
      name: 'Shoe Sizes',
      options: ['5', '6', '7', '8', '9', '10', '11']
    },
    {
      name: 'Shop By',
      options: ['New Arrivals', 'Sale', 'Just In', 'Trending']
    },
    {
      name: 'Price',
      options: ['Under £100', '£100 - £500', '£500 - £1000', 'Over £1000']
    }
  ]

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 bg-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-neutral-200">
        <h2 className="text-lg font-medium">Filter By</h2>
        <button onClick={onClose} className="p-2">
          <XMarkIcon className="h-6 w-6" />
        </button>
      </div>

      {/* Filter Content */}
      <div className="flex-1 overflow-y-auto">
        {filterCategories.map((category) => (
          <div key={category.name} className="border-b border-neutral-100">
            <button className="flex items-center justify-between w-full p-4 text-left">
              <div>
                <h3 className="font-medium text-neutral-900">{category.name}</h3>
                <p className="text-sm text-neutral-500">All</p>
              </div>
              <ChevronRightIcon className="h-5 w-5 text-neutral-400" />
            </button>
          </div>
        ))}

        {/* Just In Toggle */}
        <div className="p-4 border-b border-neutral-100">
          <div className="flex items-center justify-between">
            <span className="font-medium text-neutral-900">JUST IN</span>
            <div className="w-6 h-6 border border-neutral-300"></div>
          </div>
        </div>
      </div>

      {/* Bottom Actions */}
      <div className="p-4 border-t border-neutral-200 space-y-3">
        <button className="w-full py-3 border border-neutral-900 text-neutral-900 font-medium">
          CLEAR
        </button>
        <button className="w-full py-3 bg-black text-white font-medium">
          SHOW 1060 ITEMS
        </button>
      </div>
    </div>
  )
}