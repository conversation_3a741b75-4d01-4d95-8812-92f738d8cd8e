'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { ChevronDownIcon, AdjustmentsHorizontalIcon } from '@heroicons/react/24/outline'
import { mockProducts, filterOptions, formatPrice } from '@/lib/data'

// Use centralized data
const products = mockProducts

export default function WomenPage() {
  const [showFilters, setShowFilters] = useState(false)
  const [sortBy, setSortBy] = useState('newest')

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb */}
      <div className="border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="text-sm text-gray-500">
            <Link href="/" className="hover:text-black">Home</Link>
            <span className="mx-2">/</span>
            <span className="text-black">Women</span>
          </nav>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 py-12 lg:py-20">
        <div className="flex flex-col lg:flex-row gap-16 lg:gap-20 xl:gap-24">
          {/* Sidebar Filters */}
          <aside className={`lg:w-1/4 ${showFilters ? 'block' : 'hidden lg:block'}`}>
            <div className="space-y-12 lg:space-y-16">
              <div>
                <h3 className="font-semibold text-black mb-6 text-sm tracking-wide">DESIGNERS</h3>
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {filters.designers.map((designer) => (
                    <label key={designer} className="flex items-center text-sm text-gray-700 hover:text-black cursor-pointer">
                      <input type="checkbox" className="mr-3 w-4 h-4" />
                      {designer}
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-black mb-4">CATEGORY</h3>
                <div className="space-y-2">
                  {filters.categories.map((category) => (
                    <label key={category} className="flex items-center text-sm">
                      <input type="checkbox" className="mr-2" />
                      {category}
                    </label>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-black mb-4">SIZE</h3>
                <div className="grid grid-cols-4 gap-2">
                  {filters.sizes.map((size) => (
                    <button
                      key={size}
                      className="border border-gray-300 py-2 text-sm hover:border-black transition-colors"
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-black mb-4">COLOR</h3>
                <div className="grid grid-cols-6 gap-2">
                  {filters.colors.map((color) => (
                    <button
                      key={color}
                      className="w-8 h-8 border border-gray-300"
                      style={{ backgroundColor: color.toLowerCase() }}
                      title={color}
                    />
                  ))}
                </div>
              </div>

              <div>
                <h3 className="font-medium text-black mb-4">PRICE</h3>
                <div className="space-y-2">
                  {filters.price.map((price) => (
                    <label key={price} className="flex items-center text-sm">
                      <input type="checkbox" className="mr-2" />
                      {price}
                    </label>
                  ))}
                </div>
              </div>
            </div>
          </aside>

          {/* Main Content */}
          <main className="lg:w-3/4">
            {/* Header */}
            <div className="flex items-center justify-between mb-8">
              <div>
                <h1 className="text-3xl font-light text-black mb-2">Women</h1>
                <p className="text-gray-600">{products.length} products</p>
              </div>
              
              <div className="flex items-center gap-4">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="lg:hidden flex items-center gap-2 text-sm font-medium"
                >
                  <AdjustmentsHorizontalIcon className="h-4 w-4" />
                  Filters
                </button>
                
                <div className="relative">
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="appearance-none bg-white border border-gray-300 px-4 py-2 pr-8 text-sm focus:outline-none focus:border-black"
                  >
                    <option value="newest">Newest</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="popular">Most Popular</option>
                  </select>
                  <ChevronDownIcon className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 pointer-events-none" />
                </div>
              </div>
            </div>

            {/* Product Grid */}
            <div className="grid grid-cols-2 lg:grid-cols-3 gap-12 lg:gap-16 xl:gap-20">
              {products.map((product) => (
                <Link key={product.id} href={`/product/${product.id}`} className="group">
                  <div className="relative aspect-[3/4] mb-6 overflow-hidden bg-gray-50">
                    <Image
                      src={product.image}
                      alt={product.name}
                      fill
                      className="object-cover group-hover:scale-105 transition-transform duration-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <p className="text-xs text-gray-500 uppercase tracking-wider font-medium">{product.brand}</p>
                    <h3 className="text-sm font-normal text-black line-clamp-2 leading-relaxed">{product.name}</h3>
                    <p className="text-sm font-semibold text-black">{product.price}</p>
                    <div className="flex gap-2 mt-3">
                      {product.colors.map((color, index) => (
                        <div
                          key={index}
                          className="w-4 h-4 border border-gray-300 shadow-sm"
                          style={{ backgroundColor: color.toLowerCase() }}
                          title={color}
                        />
                      ))}
                    </div>
                  </div>
                </Link>
              ))}
            </div>

            {/* Load More */}
            <div className="text-center mt-12">
              <button className="border border-black text-black px-8 py-3 text-sm font-medium hover:bg-black hover:text-white transition-colors">
                LOAD MORE
              </button>
            </div>
          </main>
        </div>
      </div>
    </div>
  )
}
