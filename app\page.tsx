import Link from 'next/link'
import Image from 'next/image'

export default function Home() {
  return (
    <>
      {/* Hero Banner */}
      <section className="relative bg-gray-50 min-h-[90vh] flex items-center py-32 lg:py-48 xl:py-56">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 w-full">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-24 lg:gap-32 xl:gap-40 items-center">
            <div className="space-y-8 lg:space-y-10 xl:space-y-12 text-center lg:text-left">
              <h1 className="text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-light text-black leading-tight tracking-tight">
                NEW SEASON<br />HEROES
              </h1>
              <p className="text-lg lg:text-xl xl:text-2xl text-gray-600 max-w-lg xl:max-w-xl leading-relaxed mx-auto lg:mx-0">
                Discover the latest arrivals from your favorite designers
              </p>
              <div className="flex flex-col sm:flex-row gap-4 lg:gap-6 justify-center lg:justify-start pt-4 lg:pt-6">
                <Link href="/women" className="bg-black text-white px-8 py-3 text-sm font-medium tracking-wide hover:bg-gray-800 transition-colors text-center min-w-[140px]">
                  SHOP WOMEN
                </Link>
                <Link href="/men" className="border-2 border-black text-black px-8 py-3 text-sm font-medium tracking-wide hover:bg-black hover:text-white transition-colors text-center min-w-[140px]">
                  SHOP MEN
                </Link>
              </div>
            </div>
            <div className="relative h-[400px] lg:h-[500px] xl:h-[600px] w-full max-w-md lg:max-w-none mx-auto lg:mx-0">
              <Image
                src="/hero-image.jpg"
                alt="New Season Heroes"
                fill
                className="object-cover"
                priority
              />
            </div>
          </div>
        </div>
      </section>

      {/* Featured Categories */}
      <section className="py-40 lg:py-56 xl:py-64 bg-white">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
          <div className="text-center mb-28 lg:mb-36 xl:mb-44">
            <h2 className="text-4xl lg:text-5xl xl:text-6xl font-light text-black mb-12 lg:mb-16 tracking-wide">SHOP BY CATEGORY</h2>
            <div className="w-32 lg:w-40 h-px bg-black mx-auto"></div>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-12 sm:gap-16 lg:gap-20 xl:gap-24">
            {[
              { name: 'CLOTHING', count: '545 PRODUCTS', image: '/category-clothing.jpg' },
              { name: 'SHOES', count: '234 PRODUCTS', image: '/category-shoes.jpg' },
              { name: 'BAGS', count: '156 PRODUCTS', image: '/category-bags.jpg' },
              { name: 'ACCESSORIES', count: '89 PRODUCTS', image: '/category-accessories.jpg' },
              { name: 'SALE', count: 'UP TO 70% OFF', image: '/category-sale.jpg' }
            ].map((category) => (
              <Link key={category.name} href={`/${category.name.toLowerCase()}`} className="group text-center">
                <div className="relative aspect-[4/5] mb-10 lg:mb-14 xl:mb-16 overflow-hidden bg-gray-50">
                  <Image
                    src={category.image}
                    alt={category.name}
                    fill
                    className="object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                </div>
                <div className="space-y-4 lg:space-y-6">
                  <h3 className="text-base lg:text-lg xl:text-xl font-semibold text-black tracking-wide">{category.name}</h3>
                  <p className="text-sm lg:text-base text-gray-500 uppercase tracking-wide">{category.count}</p>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Editorial Content */}
      <section className="py-40 lg:py-56 xl:py-64 bg-gray-100">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-24 lg:gap-32 xl:gap-40">
            <div className="relative aspect-[3/4] group overflow-hidden">
              <Image
                src="/editorial-1.jpg"
                alt="Editorial Content"
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-700"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
              <div className="absolute bottom-10 lg:bottom-12 xl:bottom-16 left-10 lg:left-12 xl:left-16 text-white">
                <h3 className="text-2xl lg:text-3xl xl:text-4xl font-light mb-4 lg:mb-6 tracking-wide">WINTER ESSENTIALS</h3>
                <p className="text-base lg:text-lg mb-8 lg:mb-10 opacity-90">Discover our curated edit</p>
                <Link href="/winter-essentials" className="text-sm lg:text-base font-medium tracking-wide border-b border-white pb-1 hover:border-opacity-70 transition-all">
                  SHOP NOW
                </Link>
              </div>
            </div>
            <div className="space-y-8 lg:space-y-10 xl:space-y-12">
              <div className="relative aspect-[4/3] group overflow-hidden">
                <Image
                  src="/editorial-2.jpg"
                  alt="Editorial Content"
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-700"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                <div className="absolute bottom-8 lg:bottom-10 xl:bottom-12 left-8 lg:left-10 xl:left-12 text-white">
                  <h3 className="text-xl lg:text-2xl xl:text-3xl font-light mb-4 lg:mb-6 tracking-wide">THE GOLDEN HOUR</h3>
                  <Link href="/golden-hour" className="text-sm lg:text-base font-medium tracking-wide border-b border-white pb-1 hover:border-opacity-70 transition-all">
                    DISCOVER
                  </Link>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-6 lg:gap-8 xl:gap-10">
                {[
                  { name: 'DRESSES', image: '/mini-1.jpg' },
                  { name: 'KNITWEAR', image: '/mini-2.jpg' },
                  { name: 'OUTERWEAR', image: '/mini-3.jpg' }
                ].map((item) => (
                  <Link key={item.name} href={`/${item.name.toLowerCase()}`} className="group text-center">
                    <div className="relative aspect-[4/5] mb-4 lg:mb-6 xl:mb-8 overflow-hidden bg-gray-100">
                      <Image
                        src={item.image}
                        alt={item.name}
                        fill
                        className="object-cover group-hover:scale-105 transition-transform duration-500"
                      />
                    </div>
                    <p className="text-xs lg:text-sm font-semibold tracking-wide text-black">{item.name}</p>
                  </Link>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter */}
      <section className="py-40 lg:py-56 xl:py-64 bg-black text-white">
        <div className="max-w-7xl mx-auto px-6 sm:px-8 lg:px-12 text-center">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-4xl lg:text-5xl xl:text-6xl font-light mb-16 lg:mb-20 xl:mb-24 tracking-wide">SIGN UP TO OUR EMAILS</h2>
            <p className="text-gray-300 mb-20 lg:mb-28 xl:mb-32 text-xl lg:text-2xl leading-relaxed">
              Be the first to know about new arrivals, exclusive offers and style inspiration
            </p>
            <div className="flex flex-col sm:flex-row gap-8 sm:gap-12 max-w-3xl mx-auto">
              <input
                type="email"
                placeholder="Enter your email address"
                className="flex-1 px-4 sm:px-6 py-3 sm:py-4 bg-transparent border border-gray-600 text-white placeholder-gray-400 focus:outline-none focus:border-white text-base rounded-none"
              />
              <button className="bg-white text-black px-6 sm:px-8 py-3 sm:py-4 font-semibold tracking-wide hover:bg-gray-100 transition-colors text-sm whitespace-nowrap">
                SIGN UP
              </button>
            </div>
          </div>
        </div>
      </section>
    </>
  )
}